<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LangGraph Agent - Admin Panel</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-robot"></i> LangGraph Agent Admin Panel</h1>
            <div class="status-bar">
                <span class="status-item">
                    <i class="fas fa-brain"></i> Model: {{ settings.agent_model }}
                </span>
                <span class="status-item">
                    <i class="fas fa-chart-line"></i> 
                    LangSmith: {{ "Enabled" if settings.langchain_api_key else "Disabled" }}
                </span>
                <span class="status-item" id="health-status">
                    <i class="fas fa-circle" id="health-indicator"></i> Checking...
                </span>
            </div>
        </header>

        <main>
            <div class="panel-grid">
                <!-- Agent Interaction Panel -->
                <div class="panel">
                    <h2><i class="fas fa-comments"></i> Test Agent</h2>
                    <form id="agent-form">
                        <div class="form-group">
                            <label for="input-text">Enter text to summarize:</label>
                            <textarea 
                                id="input-text" 
                                name="input" 
                                rows="6" 
                                placeholder="Enter the text you want the agent to summarize..."
                                required
                            ></textarea>
                        </div>
                        <div class="form-group">
                            <label for="thread-id">Thread ID (optional):</label>
                            <input 
                                type="text" 
                                id="thread-id" 
                                name="thread_id" 
                                placeholder="Leave empty for auto-generated ID"
                            >
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Send to Agent
                        </button>
                    </form>
                    
                    <div id="loading" class="loading hidden">
                        <i class="fas fa-spinner fa-spin"></i> Processing...
                    </div>
                    
                    <div id="result" class="result hidden">
                        <h3>Agent Response:</h3>
                        <div id="result-content"></div>
                        <div id="result-metadata" class="metadata"></div>
                    </div>
                </div>

                <!-- Recent Interactions Panel -->
                <div class="panel">
                    <h2>
                        <i class="fas fa-history"></i> Recent Interactions
                        <button id="clear-interactions" class="btn btn-small btn-secondary">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </h2>
                    <div id="interactions-list">
                        {% if recent_interactions %}
                            {% for interaction in recent_interactions %}
                            <div class="interaction-item {{ 'success' if interaction.success else 'error' }}">
                                <div class="interaction-header">
                                    <span class="timestamp">{{ interaction.timestamp[:19] }}</span>
                                    <span class="status">
                                        <i class="fas {{ 'fa-check-circle' if interaction.success else 'fa-times-circle' }}"></i>
                                    </span>
                                </div>
                                <div class="interaction-input">{{ interaction.input }}</div>
                                {% if interaction.error %}
                                <div class="interaction-error">Error: {{ interaction.error }}</div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="no-interactions">No recent interactions</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Health check
        async function checkHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                const indicator = document.getElementById('health-indicator');
                const status = document.getElementById('health-status');
                
                if (response.ok) {
                    indicator.className = 'fas fa-circle text-success';
                    status.innerHTML = '<i class="fas fa-circle text-success"></i> Healthy';
                } else {
                    indicator.className = 'fas fa-circle text-error';
                    status.innerHTML = '<i class="fas fa-circle text-error"></i> Error';
                }
            } catch (error) {
                const indicator = document.getElementById('health-indicator');
                const status = document.getElementById('health-status');
                indicator.className = 'fas fa-circle text-error';
                status.innerHTML = '<i class="fas fa-circle text-error"></i> Offline';
            }
        }

        // Agent form submission
        document.getElementById('agent-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const inputText = formData.get('input');
            const threadId = formData.get('thread_id') || undefined;
            
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            loading.classList.remove('hidden');
            result.classList.add('hidden');
            
            try {
                const response = await fetch('/api/agent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        input: inputText,
                        thread_id: threadId
                    })
                });
                
                const data = await response.json();
                
                loading.classList.add('hidden');
                result.classList.remove('hidden');
                
                const resultContent = document.getElementById('result-content');
                const resultMetadata = document.getElementById('result-metadata');
                
                if (data.success) {
                    resultContent.innerHTML = `<div class="success">${data.output.replace(/\n/g, '<br>')}</div>`;
                } else {
                    resultContent.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                }
                
                if (data.metadata) {
                    resultMetadata.innerHTML = `
                        <strong>Metadata:</strong><br>
                        Model: ${data.metadata.model_used || 'N/A'}<br>
                        Processing Time: ${data.metadata.processing_time ? data.metadata.processing_time.toFixed(2) + 's' : 'N/A'}<br>
                        Thread ID: ${data.metadata.thread_id || 'N/A'}
                    `;
                }
                
                // Refresh interactions
                loadInteractions();
                
            } catch (error) {
                loading.classList.add('hidden');
                result.classList.remove('hidden');
                document.getElementById('result-content').innerHTML = 
                    `<div class="error">Network error: ${error.message}</div>`;
            }
        });

        // Load interactions
        async function loadInteractions() {
            try {
                const response = await fetch('/api/interactions');
                const data = await response.json();
                
                const interactionsList = document.getElementById('interactions-list');
                
                if (data.interactions.length === 0) {
                    interactionsList.innerHTML = '<div class="no-interactions">No recent interactions</div>';
                    return;
                }
                
                interactionsList.innerHTML = data.interactions.map(interaction => `
                    <div class="interaction-item ${interaction.success ? 'success' : 'error'}">
                        <div class="interaction-header">
                            <span class="timestamp">${interaction.timestamp.substring(0, 19)}</span>
                            <span class="status">
                                <i class="fas ${interaction.success ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                            </span>
                        </div>
                        <div class="interaction-input">${interaction.input}</div>
                        ${interaction.error ? `<div class="interaction-error">Error: ${interaction.error}</div>` : ''}
                    </div>
                `).join('');
                
            } catch (error) {
                console.error('Failed to load interactions:', error);
            }
        }

        // Clear interactions
        document.getElementById('clear-interactions').addEventListener('click', async () => {
            if (confirm('Are you sure you want to clear all interactions?')) {
                try {
                    await fetch('/api/clear-interactions', { method: 'POST' });
                    loadInteractions();
                } catch (error) {
                    alert('Failed to clear interactions');
                }
            }
        });

        // Initialize
        checkHealth();
        setInterval(checkHealth, 30000); // Check health every 30 seconds
    </script>
</body>
</html>
