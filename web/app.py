"""Flask web application for the LangGraph agent admin panel."""

import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, flash
from flask_cors import CORS

from config.settings import settings
from agent.graph import run_agent
from email_agent.scheduler import get_email_status, force_email_check, start_email_monitoring, stop_email_monitoring


def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__)
    app.secret_key = "your-secret-key-change-in-production"
    
    # Enable CORS for API endpoints
    CORS(app)
    
    return app


app = create_app()

# Store recent interactions for the admin panel
recent_interactions = []


@app.route("/")
def index():
    """Main admin panel page."""
    return render_template("index.html", 
                         recent_interactions=recent_interactions[-10:],  # Last 10 interactions
                         settings=settings)


@app.route("/api/agent", methods=["POST"])
def agent_endpoint():
    """API endpoint to interact with the agent."""
    try:
        data = request.get_json()
        
        if not data or "input" not in data:
            return jsonify({
                "success": False,
                "error": "Missing 'input' field in request"
            }), 400
        
        input_text = data["input"]
        thread_id = data.get("thread_id", str(uuid.uuid4()))
        
        # Run the agent
        result = run_agent(input_text, thread_id)
        
        # Store interaction for admin panel
        interaction = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat(),
            "input": input_text[:100] + "..." if len(input_text) > 100 else input_text,
            "output": result.get("output", "No output"),
            "success": result["success"],
            "error": result.get("error"),
            "metadata": result.get("metadata", {}),
            "thread_id": thread_id
        }
        
        recent_interactions.append(interaction)
        
        # Keep only last 50 interactions
        if len(recent_interactions) > 50:
            recent_interactions.pop(0)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Server error: {str(e)}"
        }), 500


@app.route("/api/health")
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "agent_model": settings.agent_model,
        "langsmith_enabled": bool(settings.langchain_api_key)
    })


@app.route("/api/interactions")
def get_interactions():
    """Get recent interactions."""
    return jsonify({
        "interactions": recent_interactions[-20:],  # Last 20 interactions
        "total": len(recent_interactions)
    })


@app.route("/api/clear-interactions", methods=["POST"])
def clear_interactions():
    """Clear interaction history."""
    global recent_interactions
    recent_interactions = []
    return jsonify({"success": True, "message": "Interactions cleared"})


@app.route("/api/email/status")
def email_status():
    """Get email monitoring status."""
    try:
        status = get_email_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/email/check", methods=["POST"])
def force_email_check_endpoint():
    """Force an immediate email check."""
    try:
        result = force_email_check()
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/email/start", methods=["POST"])
def start_email_monitoring_endpoint():
    """Start email monitoring."""
    try:
        start_email_monitoring()
        return jsonify({"success": True, "message": "Email monitoring started"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/email/stop", methods=["POST"])
def stop_email_monitoring_endpoint():
    """Stop email monitoring."""
    try:
        stop_email_monitoring()
        return jsonify({"success": True, "message": "Email monitoring stopped"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({"error": "Endpoint not found"}), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({"error": "Internal server error"}), 500


if __name__ == "__main__":
    print("🚀 Starting LangGraph Agent Admin Panel...")
    print(f"📊 Model: {settings.agent_model}")
    print(f"🔍 LangSmith: {'Enabled' if settings.langchain_api_key else 'Disabled'}")
    print(f"🌐 URL: http://localhost:{settings.flask_port}")
    
    app.run(
        host="0.0.0.0",
        port=settings.flask_port,
        debug=settings.flask_debug
    )
