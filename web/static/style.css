/* LangGraph Agent Admin Panel Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 2rem;
}

.status-bar {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.status-item {
    background: #f7fafc;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.9rem;
    border: 1px solid #e2e8f0;
}

.panel-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 768px) {
    .panel-grid {
        grid-template-columns: 1fr;
    }
}

.panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.panel h2 {
    color: #4a5568;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1.3rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #4a5568;
}

.form-group textarea,
.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group textarea:focus,
.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #667eea;
    font-size: 1.1rem;
}

.result {
    margin-top: 20px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.result h3 {
    margin-bottom: 15px;
    color: #4a5568;
}

.success {
    color: #38a169;
    background: #f0fff4;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #38a169;
    white-space: pre-wrap;
}

.error {
    color: #e53e3e;
    background: #fff5f5;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #e53e3e;
}

.metadata {
    margin-top: 15px;
    padding: 10px;
    background: #edf2f7;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #4a5568;
}

.interactions-list {
    max-height: 400px;
    overflow-y: auto;
}

.interaction-item {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 4px solid #e2e8f0;
}

.interaction-item.success {
    background: #f0fff4;
    border-left-color: #38a169;
}

.interaction-item.error {
    background: #fff5f5;
    border-left-color: #e53e3e;
}

.interaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.timestamp {
    font-size: 0.8rem;
    color: #718096;
}

.status i {
    font-size: 1rem;
}

.interaction-input {
    font-size: 0.9rem;
    color: #4a5568;
    margin-bottom: 5px;
}

.interaction-error {
    font-size: 0.8rem;
    color: #e53e3e;
    font-style: italic;
}

.no-interactions {
    text-align: center;
    color: #718096;
    font-style: italic;
    padding: 20px;
}

.hidden {
    display: none;
}

.text-success {
    color: #38a169;
}

.text-error {
    color: #e53e3e;
}

/* Scrollbar styling */
.interactions-list::-webkit-scrollbar {
    width: 6px;
}

.interactions-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.interactions-list::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.interactions-list::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
