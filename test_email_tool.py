#!/usr/bin/env python3
"""Test script for the email tool functionality."""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

# Load environment variables
load_dotenv()

from agent.graph import run_agent


def test_email_tool():
    """Test the email tool with a sample instruction."""
    
    # Test input that should trigger an email
    test_input = """
    Hallo! Bitte sende eine E-<NAME_EMAIL> mit dem Betreff "Test Nachricht" 
    und dem Inhalt "Dies ist eine Testnachricht vom KI-Assistenten."
    """
    
    print("🧪 Testing email tool functionality...")
    print(f"Input: {test_input.strip()}")
    print("\n" + "="*50)
    
    try:
        # Run the agent
        result = run_agent(test_input, "email-test-thread")
        
        print(f"✅ Success: {result['success']}")
        print(f"📝 Output:\n{result['output']}")
        
        if result.get('error'):
            print(f"❌ Error: {result['error']}")
        
        print(f"\n📊 Metadata: {result['metadata']}")
        
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        import traceback
        traceback.print_exc()


def test_german_response():
    """Test that the agent responds in German."""
    
    test_input = "What is artificial intelligence?"
    
    print("\n🧪 Testing German response...")
    print(f"Input: {test_input}")
    print("\n" + "="*50)
    
    try:
        result = run_agent(test_input, "german-test-thread")
        
        print(f"✅ Success: {result['success']}")
        print(f"📝 Output:\n{result['output']}")
        
        # Check if response is in German
        german_indicators = ['ist', 'eine', 'der', 'die', 'das', 'und', 'oder', 'mit', 'von']
        output_lower = result['output'].lower()
        german_words_found = [word for word in german_indicators if word in output_lower]
        
        if german_words_found:
            print(f"✅ German response detected (found words: {german_words_found})")
        else:
            print("⚠️  Response might not be in German")
        
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        import traceback
        traceback.print_exc()


def test_email_detection():
    """Test that the agent detects when to send emails."""
    
    test_input = """
    Bitte informiere Julian über das Meeting morgen um 14:00 Uhr. 
    Seine E-<NAME_EMAIL>. Sage ihm, dass wir das Projekt besprechen werden.
    """
    
    print("\n🧪 Testing email detection...")
    print(f"Input: {test_input.strip()}")
    print("\n" + "="*50)
    
    try:
        result = run_agent(test_input, "email-detection-test")
        
        print(f"✅ Success: {result['success']}")
        print(f"📝 Output:\n{result['output']}")
        
        # Check if email tool was mentioned or executed
        if "TOOL_CALL" in result['output'] or "Email" in result['output'] or "Tool-Ausführungen" in result['output']:
            print("✅ Email tool functionality detected in response")
        else:
            print("⚠️  Email tool functionality not clearly detected")
        
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 Starting email tool tests...\n")
    
    # Check if OpenAI API key is configured
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY not found in environment variables.")
        print("Please set your OpenAI API key in the .env file.")
        sys.exit(1)
    
    # Run tests
    test_german_response()
    test_email_detection()
    test_email_tool()
    
    print("\n🏁 Tests completed!")
