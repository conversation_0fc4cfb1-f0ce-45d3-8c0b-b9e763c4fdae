#!/usr/bin/env python3
"""Startup script for email agent monitoring."""

import sys
import os
import signal
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from email_agent.scheduler import start_email_monitoring, stop_email_monitoring, get_email_status
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('email_agent.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
shutdown_requested = False


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    global shutdown_requested
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_requested = True


def status_callback(status):
    """Callback function for status updates."""
    if status.get('last_result'):
        result = status['last_result']
        if result.get('success'):
            processed = result.get('processed_count', 0)
            if processed > 0:
                logger.info(f"Processed {processed} emails successfully")
        else:
            logger.error(f"Email processing failed: {result.get('error')}")


def main():
    """Main function to start email monitoring."""
    print("🚀 Starting Email Agent Monitoring...")
    print(f"📧 Email: {settings.email_username}")
    print(f"🔄 Check Interval: {settings.email_check_interval}s")
    print(f"📁 Inbox Folder: {settings.email_inbox_folder}")
    print(f"📊 Model: {settings.agent_model}")
    
    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start email monitoring
        start_email_monitoring(status_callback)
        
        logger.info("Email monitoring started successfully")
        print("✅ Email monitoring is now active")
        print("📧 Send emails with AI instructions to start processing")
        print("🛑 Press Ctrl+C to stop")
        
        # Keep the main thread alive
        while not shutdown_requested:
            try:
                # Print status every 5 minutes
                import time
                time.sleep(300)  # 5 minutes
                
                if not shutdown_requested:
                    status = get_email_status()
                    processor_status = status.get('processor_status', {})
                    
                    print(f"\n📊 Status Update - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"   Total Cycles: {status.get('total_cycles', 0)}")
                    print(f"   Success Rate: {status.get('success_rate', 0):.1f}%")
                    print(f"   Emails Processed: {processor_status.get('total_processed', 0)}")
                    print(f"   Last Sync: {processor_status.get('last_sync', 'Never')}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                time.sleep(60)  # Wait before retrying
        
    except Exception as e:
        logger.error(f"Failed to start email monitoring: {e}")
        print(f"❌ Failed to start email monitoring: {e}")
        return False
    
    finally:
        # Graceful shutdown
        print("\n🛑 Stopping email monitoring...")
        stop_email_monitoring()
        logger.info("Email monitoring stopped")
        print("✅ Email monitoring stopped successfully")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
