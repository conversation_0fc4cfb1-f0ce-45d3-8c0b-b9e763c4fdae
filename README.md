# LangGraph AI Agent

A Python-based AI agent built with LangGraph, featuring OpenAI integration, Lang<PERSON>mith monitoring, and a Flask admin panel.

## Features

- **LangGraph Agent**: Core AI agent with instruction processing capabilities
- **Email Integration**: Process AI instructions via email with IMAP/SMTP
- **Email Tool**: Send emails directly from the agent based on instructions
- **German Language Support**: Agent responds in German by default
- **OpenAI Integration**: Uses OpenAI models for text processing
- **LangSmith Monitoring**: Real-time agent monitoring and tracing
- **Flask Admin Panel**: Web interface for testing and monitoring
- **SQLite Database**: Track email processing state and prevent duplicates
- **Automated Responses**: Send AI-generated responses back via email
- **Tool System**: Extensible tool system for additional capabilities
- **Modular Architecture**: Clean, extensible codebase

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd <your-repo-name>

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys
# - Add your OpenAI API key
# - Add your LangSmith API key (optional)
```

### 3. Run the Application

```bash
# Start the Flask admin panel
python web/app.py
```

Visit `http://localhost:5000` to access the admin panel.

## Project Structure

```
├── agent/              # Core agent implementation
│   ├── graph.py       # Main LangGraph definition
│   ├── nodes.py       # Graph node implementations
│   ├── state.py       # Graph state management
│   └── tools.py       # Tool implementations (email, etc.)
├── email_agent/       # Email integration package
│   ├── connection.py  # IMAP/SMTP connection management
│   ├── processor.py   # Email processing logic
│   ├── database.py    # SQLite database for sync tracking
│   ├── scheduler.py   # Email polling scheduler
│   └── README.md      # Email agent documentation
├── config/            # Configuration management
│   └── settings.py    # Settings and environment handling
├── web/               # Flask admin panel
│   ├── app.py         # Flask application
│   ├── templates/     # HTML templates
│   └── static/        # CSS and JS files
├── tests/             # Unit tests
└── requirements.txt   # Python dependencies
```

## Usage

### Using the Agent Directly

```python
from agent.graph import create_agent_graph

# Create and run the agent
graph = create_agent_graph()
result = graph.invoke({"input": "Summarize the benefits of renewable energy"})
print(result["output"])
```

### Using the Admin Panel

1. Navigate to `http://localhost:5000`
2. Enter your prompt in the text area
3. Click "Send to Agent"
4. View the response and execution details

### Using Email Integration

1. Configure email settings in `.env` file
2. Start email monitoring:
   ```python
   from email_agent import start_email_monitoring
   start_email_monitoring()
   ```
3. Send emails with AI instructions to your configured email
4. The agent will process instructions and reply automatically

See `email_agent/README.md` for detailed email setup instructions.

### Using the Email Tool

The agent can automatically send emails when instructed to do so. The agent will:

1. **Detect email requests**: Automatically identifies when instructions require sending emails
2. **Parse email details**: Extracts recipient, subject, and body from instructions
3. **Send emails**: Uses SMTP to send emails through configured email account
4. **Provide feedback**: Reports success or failure of email sending

Example instructions that trigger email sending:
```
"Sende eine E-<NAME_EMAIL> mit dem Betreff 'Meeting Reminder' und teile ihm mit, dass das Meeting um 14:00 Uhr stattfindet."

"Bitte informiere das Team über die Projektaktualisierung. Die E-Mail-<NAME_EMAIL>."
```

The agent responds in German and will automatically format email tool calls when appropriate.

## Configuration

Key environment variables:

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `LANGCHAIN_API_KEY`: Your LangSmith API key (optional)
- `AGENT_MODEL`: OpenAI model to use (default: gpt-3.5-turbo)
- `AGENT_TEMPERATURE`: Model temperature (default: 0.7)
- `EMAIL_USERNAME`: Email account for sending emails (required for email tool)
- `EMAIL_PASSWORD`: Email password or app password (required for email tool)
- `EMAIL_SMTP_SERVER`: SMTP server for sending emails (required for email tool)
- `EMAIL_SMTP_PORT`: SMTP port (default: 587)

Email configuration (required for email functionality):
- `EMAIL_IMAP_SERVER`: IMAP server address
- `EMAIL_SMTP_SERVER`: SMTP server address
- `EMAIL_USERNAME`: Email account username
- `EMAIL_PASSWORD`: Email account password (use app password for Gmail)
- `EMAIL_CHECK_INTERVAL`: Check interval in seconds (default: 60)

## Development

### Running Tests

```bash
pytest tests/
```

### Testing Email Functionality

```bash
python test_email_agent.py
```

### Code Formatting

```bash
black .
flake8 .
```

## License

MIT License
