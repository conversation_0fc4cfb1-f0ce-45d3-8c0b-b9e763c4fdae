#!/usr/bin/env python3
"""Example usage of the email agent functionality."""

import sys
import os
import time
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from email_agent import (
    EmailProcessor, 
    EmailScheduler, 
    start_email_monitoring, 
    stop_email_monitoring,
    get_email_status,
    force_email_check
)


def example_manual_processing():
    """Example of manual email processing."""
    print("📧 Manual Email Processing Example")
    print("=" * 50)
    
    processor = EmailProcessor()
    
    # Get current status
    status = processor.get_processing_status()
    print(f"Current status: {status['status']}")
    print(f"Total processed: {status['total_processed']}")
    print(f"Success rate: {status['success_rate']:.1f}%")
    
    # Process new emails
    print("\nProcessing new emails...")
    result = processor.process_new_emails()
    
    if result['success']:
        print(f"✅ Successfully processed {result['processed_count']} emails")
        if result.get('failed_count', 0) > 0:
            print(f"⚠️ Failed to process {result['failed_count']} emails")
    else:
        print(f"❌ Processing failed: {result.get('error')}")


def example_scheduled_monitoring():
    """Example of scheduled email monitoring."""
    print("\n🕐 Scheduled Email Monitoring Example")
    print("=" * 50)
    
    # Create scheduler with 30-second interval
    scheduler = EmailScheduler(check_interval=30)
    
    def status_callback(status):
        """Callback for status updates."""
        if status.get('last_result'):
            result = status['last_result']
            if result.get('success') and result.get('processed_count', 0) > 0:
                print(f"📧 Processed {result['processed_count']} emails")
    
    try:
        print("Starting email monitoring...")
        scheduler.start(status_callback)
        
        # Let it run for 2 minutes
        print("Monitoring for 2 minutes...")
        for i in range(12):  # 12 * 10 seconds = 2 minutes
            time.sleep(10)
            status = scheduler.get_status()
            print(f"Cycle {status['total_cycles']}, Success rate: {status['success_rate']:.1f}%")
        
    finally:
        print("Stopping email monitoring...")
        scheduler.stop()


def example_global_functions():
    """Example using global convenience functions."""
    print("\n🌐 Global Functions Example")
    print("=" * 50)
    
    # Get current status
    status = get_email_status()
    print(f"Email monitoring running: {status['running']}")
    print(f"Check interval: {status['check_interval']}s")
    
    # Force an immediate check
    print("\nForcing immediate email check...")
    result = force_email_check()
    
    if result.get('success'):
        print(f"✅ Check completed: {result.get('processed_count', 0)} emails processed")
    else:
        print(f"❌ Check failed: {result.get('error')}")
    
    # Start monitoring
    print("\nStarting global email monitoring...")
    start_email_monitoring()
    
    # Let it run briefly
    time.sleep(30)
    
    # Check status
    status = get_email_status()
    print(f"Monitoring status: {status['running']}")
    print(f"Total cycles: {status['total_cycles']}")
    
    # Stop monitoring
    print("Stopping global email monitoring...")
    stop_email_monitoring()


def example_database_operations():
    """Example of database operations."""
    print("\n💾 Database Operations Example")
    print("=" * 50)
    
    from email_agent.database import EmailDatabase
    
    # Create database instance
    db = EmailDatabase("example_email_agent.db")
    
    # Get processing stats
    stats = db.get_processing_stats()
    print(f"Total processed emails: {stats['total_processed']}")
    print(f"Successful responses: {stats['successful_responses']}")
    print(f"Failed processing: {stats['failed_processing']}")
    print(f"Success rate: {stats['success_rate']:.1f}%")
    print(f"Last sync: {stats['last_sync']}")
    
    # Example of checking if email was processed
    test_uid = "example_123"
    is_processed = db.is_email_processed(test_uid)
    print(f"\nEmail {test_uid} processed: {is_processed}")
    
    # Example of marking email as processed
    if not is_processed:
        email_data = {
            'uid': test_uid,
            'sender': '<EMAIL>',
            'subject': 'Example Email',
            'received_at': datetime.now().isoformat()
        }
        
        db.mark_email_processed(
            email_data, 
            thread_id='example_thread',
            response_sent=True
        )
        print(f"Marked email {test_uid} as processed")
    
    # Clean up example database
    try:
        os.remove("example_email_agent.db")
        print("Cleaned up example database")
    except:
        pass


def example_connection_testing():
    """Example of testing email connections."""
    print("\n🔗 Connection Testing Example")
    print("=" * 50)
    
    from email_agent.connection import EmailConnection
    
    connection = EmailConnection()
    
    # Test IMAP connection
    print("Testing IMAP connection...")
    imap_success = connection.connect_imap()
    print(f"IMAP: {'✅ Connected' if imap_success else '❌ Failed'}")
    
    # Test SMTP connection
    print("Testing SMTP connection...")
    smtp_success = connection.connect_smtp()
    print(f"SMTP: {'✅ Connected' if smtp_success else '❌ Failed'}")
    
    if imap_success:
        # Try to get emails (will return empty list if no new emails)
        print("Checking for new emails...")
        emails = connection.get_new_emails()
        print(f"Found {len(emails)} new emails")
        
        for email_data in emails[:3]:  # Show first 3 emails
            print(f"  - From: {email_data['sender']}")
            print(f"    Subject: {email_data['subject']}")
            print(f"    Received: {email_data['received_at']}")
    
    # Disconnect
    connection.disconnect()
    print("Disconnected from email servers")


def main():
    """Run all examples."""
    print("🧪 Email Agent Examples")
    print("=" * 50)
    
    try:
        # Test configuration first
        from config.settings import settings
        
        required_settings = [
            'email_imap_server',
            'email_smtp_server', 
            'email_username',
            'email_password'
        ]
        
        missing = [s for s in required_settings if not getattr(settings, s, None)]
        
        if missing:
            print(f"❌ Missing email configuration: {missing}")
            print("Please configure email settings in .env file before running examples")
            return False
        
        print("✅ Email configuration found")
        
        # Run examples
        example_database_operations()
        example_connection_testing()
        example_manual_processing()
        example_global_functions()
        
        print("\n🎉 All examples completed successfully!")
        print("\nTo start continuous email monitoring:")
        print("python start_email_agent.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Example failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
