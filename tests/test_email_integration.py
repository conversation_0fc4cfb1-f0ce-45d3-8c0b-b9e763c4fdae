"""Integration tests for email agent functionality."""

import pytest
import os
import sys
import tempfile
import sqlite3
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from email_agent.database import EmailDatabase
from email_agent.processor import EmailProcessor
from email_agent.scheduler import EmailScheduler
from email_agent.connection import EmailConnection


class TestEmailDatabase:
    """Test email database functionality."""
    
    def setup_method(self):
        """Setup test database."""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = EmailDatabase(self.temp_db.name)
    
    def teardown_method(self):
        """Cleanup test database."""
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_sync_timestamp_operations(self):
        """Test sync timestamp operations."""
        # Initially should be None
        assert self.db.get_last_sync_timestamp() is None
        
        # Update timestamp
        timestamp = datetime.now().isoformat()
        self.db.update_sync_timestamp(timestamp, "test_uid")
        
        # Should retrieve the same timestamp
        retrieved = self.db.get_last_sync_timestamp()
        assert retrieved == timestamp
    
    def test_email_processing_tracking(self):
        """Test email processing tracking."""
        email_data = {
            'uid': 'test_123',
            'sender': '<EMAIL>',
            'subject': 'Test Email',
            'received_at': datetime.now().isoformat()
        }
        
        # Initially not processed
        assert not self.db.is_email_processed('test_123')
        
        # Mark as processed
        self.db.mark_email_processed(email_data, 'test_thread', True)
        
        # Should now be processed
        assert self.db.is_email_processed('test_123')
    
    def test_processing_stats(self):
        """Test processing statistics."""
        stats = self.db.get_processing_stats()
        
        assert 'total_processed' in stats
        assert 'successful_responses' in stats
        assert 'failed_processing' in stats
        assert 'success_rate' in stats


class TestEmailProcessor:
    """Test email processor functionality."""
    
    def setup_method(self):
        """Setup test processor."""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Mock the database path
        with patch('email_agent.processor.EmailDatabase') as mock_db_class:
            mock_db = Mock()
            mock_db_class.return_value = mock_db
            self.processor = EmailProcessor()
            self.mock_db = mock_db
    
    def teardown_method(self):
        """Cleanup."""
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    @patch('email_agent.processor.run_agent')
    def test_extract_instruction(self, mock_run_agent):
        """Test instruction extraction from email."""
        email_data = {
            'body': 'Please summarize the benefits of renewable energy.\n\nBest regards,\nJohn'
        }
        
        instruction = self.processor._extract_instruction(email_data)
        assert instruction == 'Please summarize the benefits of renewable energy.'
    
    def test_extract_instruction_empty(self):
        """Test instruction extraction with empty body."""
        email_data = {'body': ''}
        instruction = self.processor._extract_instruction(email_data)
        assert instruction is None
    
    def test_extract_instruction_too_short(self):
        """Test instruction extraction with too short content."""
        email_data = {'body': 'Hi'}
        instruction = self.processor._extract_instruction(email_data)
        assert instruction is None
    
    @patch('email_agent.processor.run_agent')
    def test_format_response(self, mock_run_agent):
        """Test response formatting."""
        agent_result = {
            'output': 'This is a test summary.',
            'metadata': {
                'thread_id': 'test_thread',
                'model_used': 'gpt-3.5-turbo',
                'processing_time': 1.5,
                'timestamp': '2023-01-01T00:00:00'
            }
        }
        
        email_data = {
            'sender': '<EMAIL>',
            'subject': 'Test Email'
        }
        
        response = self.processor._format_response(agent_result, email_data)
        
        assert 'This is a test summary.' in response
        assert 'test_thread' in response
        assert 'gpt-3.5-turbo' in response
        assert '1.50s' in response


class TestEmailScheduler:
    """Test email scheduler functionality."""
    
    def test_scheduler_initialization(self):
        """Test scheduler initialization."""
        scheduler = EmailScheduler(check_interval=30)
        assert scheduler.check_interval == 30
        assert not scheduler.running
        assert scheduler.total_cycles == 0
    
    def test_scheduler_status(self):
        """Test scheduler status."""
        scheduler = EmailScheduler(check_interval=30)
        status = scheduler.get_status()
        
        assert 'running' in status
        assert 'check_interval' in status
        assert 'total_cycles' in status
        assert 'success_rate' in status
    
    def test_update_interval(self):
        """Test updating check interval."""
        scheduler = EmailScheduler(check_interval=30)
        scheduler.update_interval(60)
        assert scheduler.check_interval == 60
    
    def test_update_interval_validation(self):
        """Test interval validation."""
        scheduler = EmailScheduler(check_interval=30)
        
        with pytest.raises(ValueError):
            scheduler.update_interval(5)  # Too short


class TestEmailConnection:
    """Test email connection functionality."""
    
    def test_decode_header_simple(self):
        """Test simple header decoding."""
        connection = EmailConnection()
        
        # Test simple ASCII header
        result = connection._decode_header("Test Subject")
        assert result == "Test Subject"
        
        # Test empty header
        result = connection._decode_header("")
        assert result == ""
        
        # Test None header
        result = connection._decode_header(None)
        assert result == ""
    
    def test_extract_body_simple(self):
        """Test simple body extraction."""
        connection = EmailConnection()
        
        # Create a simple email message
        import email
        from email.mime.text import MIMEText
        
        msg = MIMEText("This is a test email body.")
        body = connection._extract_body(msg)
        
        assert body == "This is a test email body."


@pytest.mark.integration
class TestEmailIntegration:
    """Integration tests requiring actual email configuration."""
    
    def test_connection_with_mock_settings(self):
        """Test connection with mocked settings."""
        with patch('email_agent.connection.settings') as mock_settings:
            mock_settings.email_imap_server = "imap.example.com"
            mock_settings.email_imap_port = 993
            mock_settings.email_smtp_server = "smtp.example.com"
            mock_settings.email_smtp_port = 587
            mock_settings.email_username = "<EMAIL>"
            mock_settings.email_password = "password"
            mock_settings.email_use_ssl = True
            mock_settings.email_use_tls = True
            mock_settings.email_inbox_folder = "INBOX"
            
            connection = EmailConnection()
            
            # These will fail without actual servers, but we can test initialization
            assert connection.imap_connection is None
            assert connection.smtp_connection is None
    
    def test_processor_status_without_connection(self):
        """Test processor status when connections fail."""
        processor = EmailProcessor()
        status = processor.get_processing_status()
        
        # Should return status even if connections fail
        assert 'status' in status
        assert 'total_processed' in status


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
