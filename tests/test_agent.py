"""Tests for the LangG<PERSON>h agent."""

import pytest
from unittest.mock import patch, MagicMock
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from agent.state import Agent<PERSON>tate, create_initial_state
from agent.nodes import validate_input_node, format_output_node, detect_language_node, extract_instructions_node
from agent.graph import create_agent_graph, should_continue_after_validation, should_continue_processing_instructions


class TestAgentState:
    """Test agent state management."""
    
    def test_create_initial_state(self):
        """Test creating initial state."""
        input_text = "Test input"
        state = create_initial_state(input_text)
        
        assert state["input"] == input_text
        assert state["output"] is None
        assert state["error"] is None
        assert state["timestamp"] is not None
        assert state["instructions"] is None
        assert state["current_instruction_index"] == 0
        assert state["instruction_results"] == []
        assert state["detected_language"] is None
        assert isinstance(state["metadata"], dict)


class TestNodes:
    """Test individual graph nodes."""
    
    def test_validate_input_node_valid_input(self):
        """Test validation with valid input."""
        state = AgentState(
            input="This is a valid input text for testing.",
            output=None,
            instructions=None,
            current_instruction_index=0,
            instruction_results=[],
            detected_language=None,
            timestamp=None,
            model_used=None,
            processing_time=None,
            error=None,
            metadata={}
        )
        
        result = validate_input_node(state)
        assert result == {}  # No state changes for valid input
    
    def test_validate_input_node_empty_input(self):
        """Test validation with empty input."""
        state = AgentState(
            input="",
            output=None,
            timestamp=None,
            model_used=None,
            processing_time=None,
            error=None,
            metadata={}
        )
        
        result = validate_input_node(state)
        assert "error" in result
        assert "empty" in result["error"].lower()
    
    def test_validate_input_node_short_input(self):
        """Test validation with too short input."""
        state = AgentState(
            input="hi",
            output=None,
            instructions=None,
            current_instruction_index=0,
            instruction_results=[],
            detected_language=None,
            timestamp=None,
            model_used=None,
            processing_time=None,
            error=None,
            metadata={}
        )

        result = validate_input_node(state)
        assert "error" in result
        assert "too short" in result["error"].lower()
    
    def test_validate_input_node_long_input(self):
        """Test validation with too long input."""
        state = AgentState(
            input="x" * 50001,  # Exceeds 50,000 character limit
            output=None,
            instructions=None,
            current_instruction_index=0,
            instruction_results=[],
            detected_language=None,
            timestamp=None,
            model_used=None,
            processing_time=None,
            error=None,
            metadata={}
        )

        result = validate_input_node(state)
        assert "error" in result
        assert "too long" in result["error"].lower()
    
    def test_format_output_node_with_error(self):
        """Test output formatting with error."""
        state = AgentState(
            input="test",
            output=None,
            instructions=None,
            current_instruction_index=0,
            instruction_results=[],
            detected_language=None,
            timestamp=None,
            model_used=None,
            processing_time=None,
            error="Test error message",
            metadata={}
        )

        result = format_output_node(state)
        assert "output" in result
        assert "Error:" in result["output"]
        assert "Test error message" in result["output"]
    
    def test_format_output_node_with_success(self):
        """Test output formatting with successful result."""
        state = AgentState(
            input="test input",
            output=None,
            instructions=["Test instruction"],
            current_instruction_index=1,
            instruction_results=[{
                "instruction": "Test instruction",
                "response": "Test response output",
                "processing_time": 1.5
            }],
            detected_language="English",
            timestamp="2023-01-01T00:00:00",
            model_used="gpt-3.5-turbo",
            processing_time=1.5,
            error=None,
            metadata={}
        )

        result = format_output_node(state)
        assert "output" in result
        assert "Test response output" in result["output"]


class TestGraph:
    """Test graph logic and flow."""
    
    def test_should_continue_after_validation_with_error(self):
        """Test conditional edge with validation error."""
        state = AgentState(
            input="test",
            output=None,
            timestamp=None,
            model_used=None,
            processing_time=None,
            error="Validation error",
            metadata={}
        )
        
        result = should_continue_after_validation(state)
        assert result == "format_output"
    
    def test_should_continue_after_validation_without_error(self):
        """Test conditional edge without validation error."""
        state = AgentState(
            input="test input that is long enough",
            output=None,
            timestamp=None,
            model_used=None,
            processing_time=None,
            error=None,
            metadata={}
        )
        
        result = should_continue_after_validation(state)
        assert result == "detect_language"
    
    def test_create_agent_graph(self):
        """Test graph creation."""
        graph = create_agent_graph()
        assert graph is not None
        
        # Test that the graph has the expected structure
        # This is a basic test - in a real scenario you might want to test
        # the actual execution flow


class TestAgentIntegration:
    """Integration tests for the complete agent."""
    
    @patch('agent.nodes.ChatOpenAI')
    def test_run_agent_success(self, mock_openai):
        """Test successful agent execution with mocked OpenAI."""
        # Mock the OpenAI response
        mock_response = MagicMock()
        mock_response.content = "This is a test summary of the input text."
        
        mock_llm = MagicMock()
        mock_llm.invoke.return_value = mock_response
        mock_openai.return_value = mock_llm
        
        # Import here to avoid issues with mocking
        from agent.graph import run_agent
        
        input_text = "This is a test input text that needs to be summarized by the agent."
        result = run_agent(input_text, "test-thread")
        
        assert result["success"] is True
        assert result["output"] is not None
        assert "This is a test summary" in result["output"]
        assert result["metadata"]["thread_id"] == "test-thread"
        assert result["error"] is None
    
    def test_run_agent_validation_error(self):
        """Test agent execution with validation error."""
        from agent.graph import run_agent
        
        # Test with empty input
        result = run_agent("", "test-thread")
        
        assert result["success"] is True  # Graph executes successfully
        assert "❌ Error:" in result["output"]
        assert "empty" in result["output"].lower()


if __name__ == "__main__":
    pytest.main([__file__])
