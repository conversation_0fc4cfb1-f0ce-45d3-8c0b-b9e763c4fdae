#!/usr/bin/env python3
"""Setup script for the LangGraph Agent project."""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def create_env_file():
    """Create .env file from template if it doesn't exist."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("🔄 Creating .env file from template...")
        env_file.write_text(env_example.read_text())
        print("✅ .env file created")
        print("⚠️  Please edit .env file with your API keys before running the application")
        return True
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("❌ .env.example file not found")
        return False


def main():
    """Main setup function."""
    print("🚀 Setting up LangGraph Agent project...\n")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check if we're in a virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  It's recommended to use a virtual environment")
        print("   Create one with: python -m venv venv")
        print("   Activate with: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)")
        
        response = input("\nContinue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled")
            sys.exit(0)
    else:
        print("✅ Virtual environment detected")
    
    print()
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    print()
    
    # Create .env file
    if not create_env_file():
        sys.exit(1)
    
    print()
    
    # Run tests to verify installation
    if run_command("python -m pytest tests/ -v", "Running tests"):
        print("✅ All tests passed")
    else:
        print("⚠️  Some tests failed, but the setup is complete")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your API keys:")
    print("   - OPENAI_API_KEY (required)")
    print("   - LANGCHAIN_API_KEY (optional, for LangSmith monitoring)")
    print("2. Run the application: python web/app.py")
    print("3. Open http://localhost:5000 in your browser")
    print("\n📚 For more information, see README.md")


if __name__ == "__main__":
    main()
