"""Email processing scheduler for continuous monitoring."""

import time
import threading
import logging
from datetime import datetime
from typing import Optional, Callable

from config.settings import settings
from .processor import EmailProcessor

logger = logging.getLogger(__name__)


class EmailScheduler:
    """Schedules and manages email processing cycles."""
    
    def __init__(self, check_interval: Optional[int] = None):
        """Initialize email scheduler.
        
        Args:
            check_interval: Check interval in seconds (uses config default if None)
        """
        self.check_interval = check_interval or settings.email_check_interval
        self.processor = EmailProcessor()
        self.running = False
        self.thread = None
        self.last_check = None
        self.total_cycles = 0
        self.successful_cycles = 0
        self.failed_cycles = 0
        
        # Callback for status updates
        self.status_callback: Optional[Callable] = None
    
    def start(self, status_callback: Optional[Callable] = None):
        """Start the email processing scheduler.
        
        Args:
            status_callback: Optional callback function for status updates
        """
        if self.running:
            logger.warning("Email scheduler is already running")
            return
        
        self.status_callback = status_callback
        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()
        
        logger.info(f"Email scheduler started with {self.check_interval}s interval")
    
    def stop(self):
        """Stop the email processing scheduler."""
        if not self.running:
            logger.warning("Email scheduler is not running")
            return
        
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=10)
        
        logger.info("Email scheduler stopped")
    
    def _run_scheduler(self):
        """Main scheduler loop."""
        logger.info("Email scheduler loop started")
        
        while self.running:
            try:
                cycle_start = datetime.now()
                self.last_check = cycle_start.isoformat()
                
                logger.debug(f"Starting email processing cycle {self.total_cycles + 1}")
                
                # Process emails
                result = self.processor.process_new_emails()
                
                self.total_cycles += 1
                
                if result.get("success"):
                    self.successful_cycles += 1
                    logger.info(f"Email cycle completed successfully: {result}")
                else:
                    self.failed_cycles += 1
                    logger.error(f"Email cycle failed: {result}")
                
                # Call status callback if provided
                if self.status_callback:
                    try:
                        status = self.get_status()
                        status['last_result'] = result
                        self.status_callback(status)
                    except Exception as e:
                        logger.error(f"Status callback failed: {e}")
                
                # Calculate sleep time
                cycle_duration = (datetime.now() - cycle_start).total_seconds()
                sleep_time = max(0, self.check_interval - cycle_duration)
                
                logger.debug(f"Cycle took {cycle_duration:.2f}s, sleeping for {sleep_time:.2f}s")
                
                # Sleep with interruption check
                self._interruptible_sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"Email scheduler cycle failed: {e}")
                self.failed_cycles += 1
                
                # Sleep before retrying
                self._interruptible_sleep(min(60, self.check_interval))
        
        logger.info("Email scheduler loop ended")
    
    def _interruptible_sleep(self, duration: float):
        """Sleep with ability to be interrupted.
        
        Args:
            duration: Sleep duration in seconds
        """
        end_time = time.time() + duration
        
        while time.time() < end_time and self.running:
            time.sleep(min(1, end_time - time.time()))
    
    def get_status(self) -> dict:
        """Get current scheduler status.
        
        Returns:
            Status dictionary
        """
        return {
            "running": self.running,
            "check_interval": self.check_interval,
            "last_check": self.last_check,
            "total_cycles": self.total_cycles,
            "successful_cycles": self.successful_cycles,
            "failed_cycles": self.failed_cycles,
            "success_rate": (self.successful_cycles / self.total_cycles * 100) if self.total_cycles > 0 else 0,
            "processor_status": self.processor.get_processing_status()
        }
    
    def force_check(self) -> dict:
        """Force an immediate email check.
        
        Returns:
            Processing result
        """
        if not self.running:
            logger.warning("Cannot force check - scheduler is not running")
            return {
                "success": False,
                "error": "Scheduler is not running"
            }
        
        logger.info("Forcing immediate email check")
        
        try:
            result = self.processor.process_new_emails()
            logger.info(f"Forced email check completed: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Forced email check failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def update_interval(self, new_interval: int):
        """Update the check interval.
        
        Args:
            new_interval: New interval in seconds
        """
        if new_interval < 10:
            raise ValueError("Check interval must be at least 10 seconds")
        
        old_interval = self.check_interval
        self.check_interval = new_interval
        
        logger.info(f"Check interval updated from {old_interval}s to {new_interval}s")


# Global scheduler instance
_scheduler_instance: Optional[EmailScheduler] = None


def get_scheduler() -> EmailScheduler:
    """Get the global scheduler instance.
    
    Returns:
        EmailScheduler instance
    """
    global _scheduler_instance
    
    if _scheduler_instance is None:
        _scheduler_instance = EmailScheduler()
    
    return _scheduler_instance


def start_email_monitoring(status_callback: Optional[Callable] = None):
    """Start email monitoring with the global scheduler.
    
    Args:
        status_callback: Optional callback for status updates
    """
    scheduler = get_scheduler()
    scheduler.start(status_callback)


def stop_email_monitoring():
    """Stop email monitoring."""
    scheduler = get_scheduler()
    scheduler.stop()


def get_email_status() -> dict:
    """Get current email monitoring status.
    
    Returns:
        Status dictionary
    """
    scheduler = get_scheduler()
    return scheduler.get_status()


def force_email_check() -> dict:
    """Force an immediate email check.
    
    Returns:
        Processing result
    """
    scheduler = get_scheduler()
    return scheduler.force_check()
