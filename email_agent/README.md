# Email Agent Package

This package provides email-based AI agent functionality, allowing the AI agent to receive instructions via email and respond automatically.

## Overview

The Email Agent system consists of several components:

- **EmailConnection**: Manages IMAP and SMTP connections
- **EmailProcessor**: Processes incoming emails and generates responses
- **EmailDatabase**: Tracks email processing state in SQLite
- **EmailScheduler**: Continuously monitors for new emails

## How It Works

1. **Email Monitoring**: The system continuously monitors an email inbox via IMAP
2. **New Email Detection**: Only processes emails received since the last sync
3. **AI Processing**: Each email spawns a new graph execution with the full email content as input
4. **Instruction Processing**: The AI agent detects language, extracts multiple instructions, and processes them sequentially
5. **Response Generation**: The AI agent generates responses in the same language as the input email
6. **Reply Sending**: The response is sent back to the original sender via SMTP
7. **State Tracking**: All processing is tracked in SQLite to prevent duplicate processing

## Configuration

Add the following environment variables to your `.env` file:

```bash
# Email Server Configuration
EMAIL_IMAP_SERVER=imap.gmail.com
EMAIL_IMAP_PORT=993
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587

# Email Credentials
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Email Settings
EMAIL_USE_SSL=true
EMAIL_USE_TLS=true
EMAIL_CHECK_INTERVAL=60
EMAIL_INBOX_FOLDER=INBOX
```

### Gmail Setup Example

For Gmail, you'll need to:

1. Enable 2-factor authentication
2. Generate an App Password
3. Use these settings:
   - IMAP Server: `imap.gmail.com:993` (SSL)
   - SMTP Server: `smtp.gmail.com:587` (TLS)

### Other Email Providers

**Outlook/Hotmail:**
```bash
EMAIL_IMAP_SERVER=outlook.office365.com
EMAIL_IMAP_PORT=993
EMAIL_SMTP_SERVER=smtp-mail.outlook.com
EMAIL_SMTP_PORT=587
```

**Yahoo:**
```bash
EMAIL_IMAP_SERVER=imap.mail.yahoo.com
EMAIL_IMAP_PORT=993
EMAIL_SMTP_SERVER=smtp.mail.yahoo.com
EMAIL_SMTP_PORT=587
```

## Usage

### Starting Email Monitoring

```python
from email_agent import start_email_monitoring, stop_email_monitoring

# Start monitoring
start_email_monitoring()

# Stop monitoring
stop_email_monitoring()
```

### Manual Email Processing

```python
from email_agent import EmailProcessor

processor = EmailProcessor()
result = processor.process_new_emails()
print(result)
```

### Checking Status

```python
from email_agent import get_email_status

status = get_email_status()
print(f"Processed emails: {status['processor_status']['total_processed']}")
print(f"Success rate: {status['processor_status']['success_rate']:.1f}%")
```

### Force Email Check

```python
from email_agent import force_email_check

result = force_email_check()
print(f"Processed {result['processed_count']} emails")
```

## Database Schema

The system uses SQLite with two main tables:

### sync_state
Tracks the last synchronization timestamp:
- `id`: Primary key
- `last_sync_timestamp`: ISO timestamp of last sync
- `last_email_uid`: UID of last processed email
- `created_at`: Record creation time
- `updated_at`: Record update time

### processed_emails
Tracks all processed emails:
- `id`: Primary key
- `email_uid`: Unique email identifier
- `sender_email`: Sender's email address
- `subject`: Email subject
- `received_at`: When email was received
- `processed_at`: When email was processed
- `response_sent`: Whether response was sent successfully
- `error_message`: Error message if processing failed
- `thread_id`: AI agent thread ID
- `created_at`: Record creation time

## Email Processing Flow

1. **Connection**: Connect to IMAP server
2. **Search**: Find unread emails since last sync
3. **Fetch**: Download email content and metadata
4. **Language Detection**: Detect the language of the email content
5. **Instruction Extraction**: Extract multiple instructions from email body
6. **Sequential Processing**: Process each instruction individually
7. **Response Generation**: Generate responses in the original email language
8. **Reply**: Send consolidated response via SMTP
9. **Track**: Record processing in database
10. **Sync**: Update last sync timestamp

## Error Handling

The system handles various error scenarios:

- **Connection failures**: Retries with exponential backoff
- **Invalid emails**: Marked as processed with error message
- **AI processing failures**: Logged and tracked in database
- **SMTP failures**: Marked as processed but response_sent=false

## Security Considerations

- Store email credentials securely in environment variables
- Use app passwords instead of main account passwords
- Consider using OAuth2 for production deployments
- Monitor failed login attempts
- Regularly rotate credentials

## Monitoring and Logging

The system provides comprehensive logging:

- Email processing cycles
- Connection status
- Processing statistics
- Error details

Use the status endpoints to monitor system health:

```python
status = get_email_status()
print(f"System status: {status['processor_status']['status']}")
print(f"IMAP connected: {status['processor_status']['imap_connection']}")
print(f"SMTP connected: {status['processor_status']['smtp_connection']}")
```

## Troubleshooting

### Common Issues

1. **Authentication failures**:
   - Check credentials
   - Verify app password for Gmail
   - Ensure 2FA is enabled

2. **Connection timeouts**:
   - Check firewall settings
   - Verify server addresses and ports
   - Test with email client first

3. **No emails processed**:
   - Check if emails are marked as read
   - Verify inbox folder name
   - Check sync timestamp in database

4. **Duplicate processing**:
   - Check database integrity
   - Verify UID tracking
   - Reset sync state if needed

### Reset Sync State

To reset the sync state and reprocess emails:

```python
from email_agent.database import EmailDatabase

db = EmailDatabase()
# This will cause all unread emails to be processed again
db.update_sync_timestamp("1970-01-01T00:00:00Z")
```

## Integration with Web Interface

The email agent can be integrated with the existing Flask web interface to provide monitoring and control capabilities. See the main application for examples of how to add email status endpoints.
