# The main TODOs for the project

## Email as Prompt input
Remove the summary processing of the graph and use the email content
as the direct input to the agent. Add a graph to process the instructions in the email.
BE AWARE that each email could also include multiple instructions.
So the graph should extract all instructions, run them in a sequence
and then return the outcome as an email reply.

## Impersonate a user in a forwarded email
The agent can receive forwarded email. In that case, the agent should impersonate
the user that forwarded the email. The agent should process the email thread
that was forwarded and reply to the user that forwarded the email.

## Add tool to send email
The agent should receive a tool that lets him send emails on it's own.
The email input could ask the agent to send someone an email,
or the task could come out of the email processing graph.
Then the agent can use the tool to send the email directly to someone.