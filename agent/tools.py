"""Tool implementations for the LangGraph agent."""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field

from config.settings import settings

logger = logging.getLogger(__name__)


class EmailTool(BaseModel):
    """Tool for sending emails."""
    
    name: str = "send_email"
    description: str = "Send an email to a recipient"
    
    def __call__(self, to_email: str, subject: str, body: str, from_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Send an email using SMTP.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            body: Email body content
            from_name: Optional sender name (defaults to configured email)
            
        Returns:
            Dictionary with success status and message
        """
        try:
            # Validate email settings
            if not all([settings.email_smtp_server, settings.email_smtp_port, 
                       settings.email_username, settings.email_password]):
                return {
                    "success": False,
                    "error": "Email configuration is incomplete. Please check SMTP settings."
                }
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = f"{from_name or settings.email_username} <{settings.email_username}>"
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add body to email
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # Create SMTP session
            server = smtplib.SMTP(settings.email_smtp_server, settings.email_smtp_port)
            server.starttls()  # Enable security
            server.login(settings.email_username, settings.email_password)
            
            # Send email
            text = msg.as_string()
            server.sendmail(settings.email_username, to_email, text)
            server.quit()
            
            logger.info(f"Email sent successfully to {to_email}")
            return {
                "success": True,
                "message": f"Email erfolgreich an {to_email} gesendet"
            }
            
        except Exception as e:
            error_msg = f"Fehler beim Senden der Email: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }


# Tool instances
email_tool = EmailTool()

# Available tools dictionary
AVAILABLE_TOOLS = {
    "send_email": email_tool
}


def get_tool_descriptions() -> str:
    """Get descriptions of all available tools for the system prompt."""
    descriptions = []
    for tool_name, tool in AVAILABLE_TOOLS.items():
        descriptions.append(f"- {tool_name}: {tool.description}")
    return "\n".join(descriptions)


def execute_tool(tool_name: str, **kwargs) -> Dict[str, Any]:
    """
    Execute a tool by name with given arguments.
    
    Args:
        tool_name: Name of the tool to execute
        **kwargs: Arguments to pass to the tool
        
    Returns:
        Tool execution result
    """
    if tool_name not in AVAILABLE_TOOLS:
        return {
            "success": False,
            "error": f"Tool '{tool_name}' not found. Available tools: {list(AVAILABLE_TOOLS.keys())}"
        }
    
    try:
        tool = AVAILABLE_TOOLS[tool_name]
        return tool(**kwargs)
    except Exception as e:
        return {
            "success": False,
            "error": f"Error executing tool '{tool_name}': {str(e)}"
        }
