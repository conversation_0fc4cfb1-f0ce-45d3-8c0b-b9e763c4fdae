"""Node implementations for the LangGraph agent."""

import time
import re
import json
from typing import Dict, Any, List
from langchain_openai import Chat<PERSON>penAI
from langchain.schema import HumanMessage, SystemMessage

from config.settings import settings
from agent.state import AgentState
from agent.tools import execute_tool, get_tool_descriptions


def detect_language_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that detects the language of the input text.

    Args:
        state: Current agent state

    Returns:
        Updated state with detected language
    """
    try:
        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.1,  # Low temperature for consistent language detection
            max_tokens=50,
            openai_api_key=settings.openai_api_key
        )

        # Create the prompt for language detection
        system_message = SystemMessage(content="""
You are a language detection assistant. Your task is to identify the language of the given text.
Respond with only the language name in English (e.g., "English", "Spanish", "French", "German", etc.).
If you cannot determine the language, respond with "English" as the default.
""")

        human_message = HumanMessage(content=f"What language is this text written in?\n\n{state['input'][:500]}")

        # Get the response from OpenAI
        response = llm.invoke([system_message, human_message])
        detected_language = response.content.strip()

        return {
            "detected_language": detected_language
        }

    except Exception as e:
        # Default to English if detection fails
        return {
            "detected_language": "English"
        }


def extract_instructions_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that extracts individual instructions from the email content.

    Args:
        state: Current agent state

    Returns:
        Updated state with extracted instructions
    """
    try:
        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=0.3,
            max_tokens=1000,
            openai_api_key=settings.openai_api_key
        )

        # Create the prompt for instruction extraction
        system_message = SystemMessage(content="""
You are an instruction extraction assistant. Your task is to identify and extract individual actionable instructions from email content.

Guidelines:
- Extract each distinct instruction or request as a separate item
- Ignore greetings, signatures, and pleasantries
- Focus on actionable requests, questions, or tasks
- If there are no clear instructions, return the main content as a single instruction
- Return each instruction on a new line, numbered (1., 2., 3., etc.)
- Keep instructions clear and complete

Example:
Input: "Hi John, Please send me the quarterly report. Also, can you schedule a meeting for next week? Thanks!"
Output:
1. Send the quarterly report
2. Schedule a meeting for next week
""")

        human_message = HumanMessage(content=f"Extract the instructions from this email:\n\n{state['input']}")

        # Get the response from OpenAI
        response = llm.invoke([system_message, human_message])

        # Parse the numbered instructions
        instructions = []
        lines = response.content.strip().split('\n')

        for line in lines:
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('•') or line.startswith('-')):
                # Remove numbering/bullets and clean up
                instruction = re.sub(r'^\d+\.\s*|^[•\-]\s*', '', line).strip()
                if instruction:
                    instructions.append(instruction)

        # If no numbered instructions found, treat the whole response as one instruction
        if not instructions:
            clean_response = response.content.strip()
            if clean_response:
                instructions = [clean_response]

        # If still no instructions, use the original input
        if not instructions:
            instructions = [state['input']]

        return {
            "instructions": instructions,
            "current_instruction_index": 0
        }

    except Exception as e:
        # Fallback: treat the entire input as a single instruction
        return {
            "instructions": [state['input']],
            "current_instruction_index": 0,
            "error": f"Error in instruction extraction: {str(e)}"
        }


def process_instruction_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that processes a single instruction using OpenAI.

    Args:
        state: Current agent state

    Returns:
        Updated state with instruction result
    """
    start_time = time.time()

    try:
        instructions = state.get("instructions", [])
        current_index = state.get("current_instruction_index", 0)
        detected_language = state.get("detected_language", "English")

        if current_index >= len(instructions):
            return {
                "error": "No more instructions to process"
            }

        current_instruction = instructions[current_index]

        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=settings.agent_temperature,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )

        # Create the prompt for instruction processing
        system_message = SystemMessage(content=f"""
Du bist ein hilfreicher KI-Assistent, der Anweisungen verarbeitet und nützliche Antworten liefert.
Deine Aufgabe ist es, die Anweisung zu verstehen und eine hilfreiche, genaue Antwort zu geben.

Wichtige Richtlinien:
- Antworte IMMER auf Deutsch
- Sei hilfreich, genau und präzise
- Wenn du eine Aufgabe nicht ausführen kannst, erkläre was du kannst und was nicht
- Gib umsetzbare Informationen wenn möglich
- Sei professionell und höflich

Verfügbare Tools:
{get_tool_descriptions()}

WICHTIG: Überlege bei jeder Anweisung, ob du eine E-Mail senden solltest:
- Wenn die Anweisung darauf hindeutet, dass jemand informiert werden sollte
- Wenn eine Bestätigung oder Rückmeldung erforderlich ist
- Wenn explizit nach dem Senden einer E-Mail gefragt wird

Wenn du eine E-Mail senden möchtest, gib am Ende deiner Antwort folgendes Format an:
TOOL_CALL: send_email
TO: <EMAIL>
SUBJECT: Betreff der E-Mail
BODY: Inhalt der E-Mail

Deine Antworten müssen auf Deutsch sein.
""")

        human_message = HumanMessage(content=f"Please process this instruction:\n\n{current_instruction}")

        # Get the response from OpenAI
        response = llm.invoke([system_message, human_message])

        # Calculate processing time
        processing_time = time.time() - start_time

        # Store the result
        instruction_results = state.get("instruction_results", [])
        instruction_results.append({
            "instruction": current_instruction,
            "response": response.content,
            "processing_time": processing_time
        })

        return {
            "instruction_results": instruction_results,
            "current_instruction_index": current_index + 1,
            "model_used": settings.agent_model,
            "processing_time": processing_time
        }

    except Exception as e:
        processing_time = time.time() - start_time
        return {
            "error": f"Error processing instruction: {str(e)}",
            "processing_time": processing_time
        }


def validate_input_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that validates the input before processing.

    Args:
        state: Current agent state

    Returns:
        Updated state with validation results
    """
    input_text = state.get("input", "")

    # Basic validation
    if not input_text or not input_text.strip():
        return {
            "error": "Input text is empty or contains only whitespace"
        }

    # Check if input is too short to process meaningfully
    if len(input_text.strip()) < 5:
        return {
            "error": "Input text is too short to process (minimum 5 characters)"
        }

    # Check if input is extremely long (optional limit)
    if len(input_text) > 50000:
        return {
            "error": "Input text is too long (maximum 50,000 characters)"
        }

    # If validation passes, return empty dict (no state changes)
    return {}


def format_output_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that formats the final output from all processed instructions.

    Args:
        state: Current agent state

    Returns:
        Updated state with formatted output
    """
    if state.get("error"):
        # If there's an error, return it simply without formatting
        return {
            "output": f"Fehler: {state['error']}"
        }

    instruction_results = state.get("instruction_results", [])
    tool_results = state.get("tool_results", [])

    if not instruction_results:
        return {
            "output": "Keine Anweisungen wurden verarbeitet."
        }

    # Format instruction responses
    if len(instruction_results) == 1:
        # Single instruction - return just the response
        output = instruction_results[0]["response"]
    else:
        # Multiple instructions - format as a list
        formatted_responses = []
        for i, result in enumerate(instruction_results, 1):
            formatted_responses.append(f"{i}. {result['response']}")
        output = "\n\n".join(formatted_responses)

    # Add tool execution results if any
    if tool_results:
        output += "\n\n--- Tool-Ausführungen ---\n"
        for tool_result in tool_results:
            tool_name = tool_result.get("tool", "Unbekannt")
            result = tool_result.get("result", {})

            if result.get("success"):
                output += f"✅ {tool_name}: {result.get('message', 'Erfolgreich ausgeführt')}\n"
            else:
                output += f"❌ {tool_name}: {result.get('error', 'Fehler bei der Ausführung')}\n"

    return {
        "output": output
    }


def detect_and_execute_tools_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that detects tool calls in instruction results and executes them.

    Args:
        state: Current agent state

    Returns:
        Updated state with tool execution results
    """
    instruction_results = state.get("instruction_results", [])
    tool_results = state.get("tool_results", [])

    if not instruction_results:
        return {}

    # Check the last instruction result for tool calls
    last_result = instruction_results[-1]
    response_text = last_result.get("response", "")

    # Parse tool calls from the response
    tool_calls = _parse_tool_calls(response_text)

    if not tool_calls:
        return {}

    # Execute each tool call
    for tool_call in tool_calls:
        try:
            tool_name = tool_call.get("tool")
            tool_args = tool_call.get("args", {})

            # Execute the tool
            result = execute_tool(tool_name, **tool_args)

            # Store the result
            tool_results.append({
                "tool": tool_name,
                "args": tool_args,
                "result": result,
                "instruction_index": len(instruction_results) - 1
            })

        except Exception as e:
            tool_results.append({
                "tool": tool_call.get("tool", "unknown"),
                "args": tool_call.get("args", {}),
                "result": {
                    "success": False,
                    "error": f"Fehler bei der Tool-Ausführung: {str(e)}"
                },
                "instruction_index": len(instruction_results) - 1
            })

    return {
        "tool_calls": tool_calls,
        "tool_results": tool_results
    }


def _parse_tool_calls(response_text: str) -> List[Dict[str, Any]]:
    """
    Parse tool calls from response text.

    Args:
        response_text: The response text to parse

    Returns:
        List of tool call dictionaries
    """
    tool_calls = []
    lines = response_text.split('\n')

    current_tool_call = None

    for line in lines:
        line = line.strip()

        if line.startswith("TOOL_CALL:"):
            if current_tool_call:
                tool_calls.append(current_tool_call)

            tool_name = line.replace("TOOL_CALL:", "").strip()
            current_tool_call = {
                "tool": tool_name,
                "args": {}
            }

        elif current_tool_call and line.startswith("TO:"):
            current_tool_call["args"]["to_email"] = line.replace("TO:", "").strip()

        elif current_tool_call and line.startswith("SUBJECT:"):
            current_tool_call["args"]["subject"] = line.replace("SUBJECT:", "").strip()

        elif current_tool_call and line.startswith("BODY:"):
            current_tool_call["args"]["body"] = line.replace("BODY:", "").strip()

    # Add the last tool call if exists
    if current_tool_call:
        tool_calls.append(current_tool_call)

    return tool_calls
