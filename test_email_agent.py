#!/usr/bin/env python3
"""Test script for email agent functionality."""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from email_agent.connection import EmailConnection
from email_agent.processor import EmailProcessor
from email_agent.database import EmailDatabase
from email_agent.scheduler import EmailScheduler, start_email_monitoring, stop_email_monitoring, get_email_status
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_email_connection():
    """Test email connection functionality."""
    print("🔗 Testing Email Connection...")
    
    connection = EmailConnection()
    
    # Test IMAP connection
    print("Testing IMAP connection...")
    imap_success = connection.connect_imap()
    print(f"IMAP Connection: {'✅ Success' if imap_success else '❌ Failed'}")
    
    # Test SMTP connection
    print("Testing SMTP connection...")
    smtp_success = connection.connect_smtp()
    print(f"SMTP Connection: {'✅ Success' if smtp_success else '❌ Failed'}")
    
    # Disconnect
    connection.disconnect()
    
    return imap_success and smtp_success


def test_database():
    """Test database functionality."""
    print("\n💾 Testing Database...")
    
    db = EmailDatabase("test_email_agent.db")
    
    # Test sync timestamp
    print("Testing sync timestamp...")
    timestamp = datetime.now().isoformat()
    db.update_sync_timestamp(timestamp, "test_uid")
    
    retrieved_timestamp = db.get_last_sync_timestamp()
    print(f"Sync timestamp: {'✅ Success' if retrieved_timestamp == timestamp else '❌ Failed'}")
    
    # Test email processing tracking
    print("Testing email processing tracking...")
    test_email = {
        'uid': 'test_123',
        'sender': '<EMAIL>',
        'subject': 'Test Email',
        'received_at': timestamp
    }
    
    # Check if processed (should be False)
    is_processed_before = db.is_email_processed('test_123')
    print(f"Email not processed initially: {'✅ Success' if not is_processed_before else '❌ Failed'}")
    
    # Mark as processed
    db.mark_email_processed(test_email, 'test_thread', True)
    
    # Check if processed (should be True)
    is_processed_after = db.is_email_processed('test_123')
    print(f"Email marked as processed: {'✅ Success' if is_processed_after else '❌ Failed'}")
    
    # Get stats
    stats = db.get_processing_stats()
    print(f"Database stats: {stats}")
    
    # Clean up test database
    try:
        os.remove("test_email_agent.db")
        print("Test database cleaned up")
    except:
        pass
    
    return True


def test_email_processor():
    """Test email processor functionality."""
    print("\n⚙️ Testing Email Processor...")
    
    processor = EmailProcessor()
    
    # Get status
    status = processor.get_processing_status()
    print(f"Processor status: {status}")
    
    return status.get('status') in ['healthy', 'degraded']


def test_scheduler():
    """Test email scheduler functionality."""
    print("\n⏰ Testing Email Scheduler...")
    
    scheduler = EmailScheduler(check_interval=5)  # 5 second interval for testing
    
    # Get initial status
    status = scheduler.get_status()
    print(f"Initial scheduler status: {status}")
    
    # Test start/stop
    print("Testing scheduler start/stop...")
    scheduler.start()
    
    import time
    time.sleep(2)  # Let it run briefly
    
    running_status = scheduler.get_status()
    print(f"Running status: {running_status}")
    
    scheduler.stop()
    
    stopped_status = scheduler.get_status()
    print(f"Stopped status: {stopped_status}")
    
    return True


def test_configuration():
    """Test configuration settings."""
    print("\n⚙️ Testing Configuration...")
    
    required_settings = [
        'email_imap_server',
        'email_smtp_server',
        'email_username',
        'email_password'
    ]
    
    missing_settings = []
    
    for setting in required_settings:
        try:
            value = getattr(settings, setting)
            if not value:
                missing_settings.append(setting)
        except AttributeError:
            missing_settings.append(setting)
    
    if missing_settings:
        print(f"❌ Missing required settings: {missing_settings}")
        print("Please check your .env file and ensure all email settings are configured.")
        return False
    else:
        print("✅ All required email settings are configured")
        return True


def main():
    """Run all tests."""
    print("🧪 Email Agent Test Suite")
    print("=" * 50)
    
    # Test configuration first
    config_ok = test_configuration()
    if not config_ok:
        print("\n❌ Configuration test failed. Please fix configuration before proceeding.")
        return False
    
    # Test database
    db_ok = test_database()
    
    # Test email connection (only if config is OK)
    connection_ok = test_email_connection() if config_ok else False
    
    # Test processor
    processor_ok = test_email_processor()
    
    # Test scheduler
    scheduler_ok = test_scheduler()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    print(f"Configuration: {'✅ Pass' if config_ok else '❌ Fail'}")
    print(f"Database: {'✅ Pass' if db_ok else '❌ Fail'}")
    print(f"Email Connection: {'✅ Pass' if connection_ok else '❌ Fail'}")
    print(f"Email Processor: {'✅ Pass' if processor_ok else '❌ Fail'}")
    print(f"Email Scheduler: {'✅ Pass' if scheduler_ok else '❌ Fail'}")
    
    all_passed = all([config_ok, db_ok, connection_ok, processor_ok, scheduler_ok])
    
    if all_passed:
        print("\n🎉 All tests passed! Email agent is ready to use.")
        print("\nTo start email monitoring:")
        print("python -c \"from email_agent import start_email_monitoring; start_email_monitoring()\"")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration and try again.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
